{"document_to_check": "template.docx", "title_rules": {"expected_titles": [{"text": "测试工具版本", "style_name": "Heading 4", "required": true}, {"text": "适用产品", "style_name": "Heading 4", "required": true}, {"text": "BIOS设置", "style_name": "Heading 4", "required": true}, {"text": "OS设置", "style_name": "Heading 4", "required": true}, {"text": "环境部署", "style_name": "Heading 4", "required": true}, {"text": "测试执行", "style_name": "Heading 4", "required": true}]}, "table_rules": [{"heading_text": "测试工具版本", "table_index": 0, "all_cells_not_empty": true}, {"heading_text": "适用产品", "table_index": 0, "all_cells_not_empty": true, "column_value_check": {"column_header": "加速卡", "allowed_values": ["GPU-BR106B", "GPU-BR106C", "GPU-MLU370-X4", "GPU-MLU370-X8", "GPU-MLU590", "GPU-S60", "GPU-K100-AI", "GPU-BI-V100", "GPU-BI-V150", "GPU-BI-V150X", "GPU-MR-V50", "GPU-MR-V100", "GPU-P800 8-GPU", "GPU-P800", "GPU-R200", "GPU-R200-8F", "GPU-R300", "GPU-RG800 Pro", "GPU-RG800", "GPU-HP280", "GPU-C500", "GPU-C500X", "GPU-MXC550 8GPU", "GPU-S4000", "GPU-L20", "GPU-HGX H20 8-GPU", "GPU-S30"], "allow_empty_columns": ["备注"]}}], "content_under_heading_rules": [{"heading_text_exact": "BIOS设置", "check_next_paragraphs": 1, "not_empty": true}, {"heading_text_exact": "OS设置", "check_next_paragraphs": 1, "not_empty": true}, {"heading_text_exact": "环境部署", "check_next_paragraphs": 1, "not_empty": true}, {"heading_text_exact": "测试执行", "check_next_paragraphs": 1, "not_empty": true}]}