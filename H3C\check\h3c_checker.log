2025-05-23 16:15:52,554 - root - INFO - ==================================================
2025-05-23 16:15:52,554 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:15:52,554 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:15:52,554 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:15:52,554 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:15:52,554 - root - INFO - 导入主模块...
2025-05-23 16:15:53,141 - root - INFO - 启动主模块...
2025-05-23 16:15:58,551 - root - INFO - ==================================================
2025-05-23 16:15:58,552 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:15:58,552 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:15:58,552 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:15:58,552 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:15:58,552 - root - INFO - 导入主模块...
2025-05-23 16:15:58,931 - root - INFO - 启动主模块...
2025-05-23 16:15:58,936 - root - INFO - 准备启动GUI...
2025-05-23 16:15:58,936 - root - INFO - 测试GUI环境...
2025-05-23 16:15:59,165 - root - INFO - GUI环境测试通过
2025-05-23 16:15:59,184 - root - INFO - 导入GUI模块...
2025-05-23 16:15:59,212 - root - INFO - 正在创建主窗口...
2025-05-23 16:15:59,319 - root - INFO - GUI启动成功
2025-05-23 16:16:25,335 - root - INFO - ==================================================
2025-05-23 16:16:25,336 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:16:25,336 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:16:25,336 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:16:25,336 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:16:25,336 - root - INFO - 导入主模块...
2025-05-23 16:16:25,759 - root - INFO - 启动主模块...
2025-05-23 16:16:25,763 - root - INFO - 准备启动GUI...
2025-05-23 16:16:25,764 - root - INFO - 测试GUI环境...
2025-05-23 16:16:25,989 - root - INFO - GUI环境测试通过
2025-05-23 16:16:26,006 - root - INFO - 导入GUI模块...
2025-05-23 16:16:26,028 - root - INFO - 正在创建主窗口...
2025-05-23 16:16:26,143 - root - INFO - GUI启动成功
2025-05-23 16:17:35,026 - root - INFO - ==================================================
2025-05-23 16:17:35,026 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:17:35,026 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:17:35,026 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:17:35,027 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:17:35,027 - root - INFO - 导入主模块...
2025-05-23 16:17:35,417 - root - INFO - 启动主模块...
2025-05-23 16:17:35,421 - root - INFO - 准备启动GUI...
2025-05-23 16:17:35,421 - root - INFO - 测试GUI环境...
2025-05-23 16:17:35,650 - root - INFO - GUI环境测试通过
2025-05-23 16:17:35,670 - root - INFO - 导入GUI模块...
2025-05-23 16:17:35,685 - root - INFO - 正在创建主窗口...
2025-05-23 16:17:35,805 - root - INFO - GUI启动成功
2025-05-23 16:18:27,567 - root - INFO - ==================================================
2025-05-23 16:18:27,567 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:18:27,567 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:18:27,568 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:18:27,568 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:18:27,568 - root - INFO - 导入主模块...
2025-05-23 16:18:27,964 - root - INFO - 启动主模块...
2025-05-23 16:18:27,969 - root - INFO - 准备启动GUI...
2025-05-23 16:18:27,969 - root - INFO - 测试GUI环境...
2025-05-23 16:18:28,216 - root - INFO - GUI环境测试通过
2025-05-23 16:18:28,238 - root - INFO - 导入GUI模块...
2025-05-23 16:18:28,256 - root - INFO - 正在创建主窗口...
2025-05-23 16:18:28,396 - root - INFO - GUI启动成功
2025-05-23 16:27:01,909 - root - INFO - ==================================================
2025-05-23 16:27:01,909 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:27:01,909 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:27:01,909 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:27:01,909 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:27:01,910 - root - INFO - 导入主模块...
2025-05-23 16:27:02,272 - root - INFO - 启动主模块...
2025-05-23 16:27:02,276 - root - INFO - 准备启动GUI...
2025-05-23 16:27:02,276 - root - INFO - 测试GUI环境...
2025-05-23 16:27:02,520 - root - INFO - GUI环境测试通过
2025-05-23 16:27:02,542 - root - INFO - 导入GUI模块...
2025-05-23 16:27:02,566 - root - INFO - 正在创建主窗口...
2025-05-23 16:27:02,675 - root - INFO - GUI启动成功
2025-05-23 16:29:18,557 - root - INFO - ==================================================
2025-05-23 16:29:18,557 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:29:18,558 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:29:18,558 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:29:18,558 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:29:18,558 - root - INFO - 导入主模块...
2025-05-23 16:29:18,561 - root - ERROR - 程序出错: unterminated string literal (detected at line 175) (main.py, line 175)
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\__main__.py", line 34, in main
    from h3c_doc_checker.main import main as main_module
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\main.py", line 175
    help="配置文件路径（可选）
         ^
SyntaxError: unterminated string literal (detected at line 175)
2025-05-23 16:30:03,163 - root - INFO - ==================================================
2025-05-23 16:30:03,163 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:30:03,164 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:30:03,164 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:30:03,164 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:30:03,164 - root - INFO - 导入主模块...
2025-05-23 16:30:03,541 - root - INFO - 启动主模块...
2025-05-23 16:30:03,545 - root - INFO - 准备启动GUI...
2025-05-23 16:30:03,545 - root - INFO - 测试GUI环境...
2025-05-23 16:30:03,773 - root - INFO - GUI环境测试通过
2025-05-23 16:30:03,790 - root - INFO - 导入GUI模块...
2025-05-23 16:30:03,809 - root - INFO - 正在创建主窗口...
2025-05-23 16:30:03,918 - root - INFO - GUI启动成功
2025-05-23 16:47:47,450 - root - INFO - ==================================================
2025-05-23 16:47:47,451 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:47:47,451 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:47:47,451 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:47:47,451 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:47:47,451 - root - INFO - 导入主模块...
2025-05-23 16:47:47,871 - root - INFO - 启动主模块...
2025-05-23 16:47:47,876 - root - INFO - 准备启动GUI...
2025-05-23 16:47:47,876 - root - INFO - 测试GUI环境...
2025-05-23 16:47:48,121 - root - INFO - GUI环境测试通过
2025-05-23 16:47:48,138 - root - INFO - 导入GUI模块...
2025-05-23 16:47:48,159 - root - INFO - 正在创建主窗口...
2025-05-23 16:47:48,267 - root - INFO - GUI启动成功
2025-05-23 16:48:55,890 - root - INFO - ==================================================
2025-05-23 16:48:55,890 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:48:55,890 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:48:55,890 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:48:55,891 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:48:55,891 - root - INFO - 导入主模块...
2025-05-23 16:48:56,274 - root - INFO - 启动主模块...
2025-05-23 16:48:56,278 - root - INFO - 准备启动GUI...
2025-05-23 16:48:56,279 - root - INFO - 测试GUI环境...
2025-05-23 16:48:56,510 - root - INFO - GUI环境测试通过
2025-05-23 16:48:56,524 - root - INFO - 导入GUI模块...
2025-05-23 16:48:56,540 - root - INFO - 正在创建主窗口...
2025-05-23 16:48:56,646 - root - INFO - GUI启动成功
2025-05-23 16:57:04,530 - root - INFO - ==================================================
2025-05-23 16:57:04,530 - root - INFO - H3C Doc Checker 启动
2025-05-23 16:57:04,531 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 16:57:04,531 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 16:57:04,531 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 16:57:04,531 - root - INFO - 导入主模块...
2025-05-23 16:57:04,925 - root - INFO - 启动主模块...
2025-05-23 16:57:04,930 - root - INFO - 准备启动GUI...
2025-05-23 16:57:04,931 - root - INFO - 测试GUI环境...
2025-05-23 16:57:05,175 - root - INFO - GUI环境测试通过
2025-05-23 16:57:05,199 - root - INFO - 导入GUI模块...
2025-05-23 16:57:05,221 - root - INFO - 正在创建主窗口...
2025-05-23 16:57:05,333 - root - INFO - GUI启动成功
2025-05-23 17:05:16,575 - root - INFO - ==================================================
2025-05-23 17:05:16,576 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:05:16,576 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:05:16,576 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:05:16,576 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:05:16,576 - root - INFO - 导入主模块...
2025-05-23 17:05:16,995 - root - INFO - 启动主模块...
2025-05-23 17:05:17,003 - root - INFO - 准备启动GUI...
2025-05-23 17:05:17,003 - root - INFO - 测试GUI环境...
2025-05-23 17:05:17,247 - root - INFO - GUI环境测试通过
2025-05-23 17:05:17,265 - root - INFO - 导入GUI模块...
2025-05-23 17:05:17,288 - root - INFO - 正在创建主窗口...
2025-05-23 17:05:17,413 - root - INFO - GUI启动成功
2025-05-23 17:10:23,709 - root - INFO - ==================================================
2025-05-23 17:10:23,710 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:10:23,710 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:10:23,710 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:10:23,711 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:10:23,711 - root - INFO - 导入主模块...
2025-05-23 17:10:24,850 - root - INFO - 启动主模块...
2025-05-23 17:10:24,854 - root - INFO - 准备启动GUI...
2025-05-23 17:10:24,854 - root - INFO - 测试GUI环境...
2025-05-23 17:10:25,143 - root - INFO - GUI环境测试通过
2025-05-23 17:10:25,162 - root - INFO - 导入GUI模块...
2025-05-23 17:10:25,182 - root - INFO - 正在创建主窗口...
2025-05-23 17:10:25,437 - root - INFO - GUI启动成功
2025-05-23 17:11:15,392 - root - INFO - ==================================================
2025-05-23 17:11:15,392 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:11:15,392 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:11:15,393 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:11:15,393 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:11:15,393 - root - INFO - 导入主模块...
2025-05-23 17:11:15,845 - root - INFO - 启动主模块...
2025-05-23 17:11:15,849 - root - INFO - 准备启动GUI...
2025-05-23 17:11:15,849 - root - INFO - 测试GUI环境...
2025-05-23 17:11:16,113 - root - INFO - GUI环境测试通过
2025-05-23 17:11:16,135 - root - INFO - 导入GUI模块...
2025-05-23 17:11:16,151 - root - INFO - 正在创建主窗口...
2025-05-23 17:11:16,283 - root - INFO - GUI启动成功
2025-05-23 17:12:19,383 - root - INFO - ==================================================
2025-05-23 17:12:19,383 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:12:19,383 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:12:19,383 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:12:19,384 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:12:19,384 - root - INFO - 导入主模块...
2025-05-23 17:12:20,155 - root - INFO - 启动主模块...
2025-05-23 17:12:20,159 - root - INFO - 准备启动GUI...
2025-05-23 17:12:20,159 - root - INFO - 测试GUI环境...
2025-05-23 17:12:20,428 - root - INFO - GUI环境测试通过
2025-05-23 17:12:20,448 - root - INFO - 导入GUI模块...
2025-05-23 17:12:20,464 - root - INFO - 正在创建主窗口...
2025-05-23 17:12:20,584 - root - INFO - GUI启动成功
2025-05-23 17:13:29,786 - root - INFO - ==================================================
2025-05-23 17:13:29,786 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:13:29,786 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:13:29,787 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:13:29,787 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:13:29,787 - root - INFO - 导入主模块...
2025-05-23 17:13:35,211 - root - INFO - 启动主模块...
2025-05-23 17:13:35,215 - root - INFO - 准备启动GUI...
2025-05-23 17:13:35,216 - root - INFO - 测试GUI环境...
2025-05-23 17:13:35,377 - root - INFO - GUI环境测试通过
2025-05-23 17:13:35,380 - root - INFO - 导入GUI模块...
2025-05-23 17:13:35,394 - root - INFO - 正在创建主窗口...
2025-05-23 17:13:35,520 - root - INFO - GUI启动成功
2025-05-23 17:15:15,996 - root - INFO - ==================================================
2025-05-23 17:15:15,996 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:15:15,996 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:15:15,996 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:15:15,996 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:15:15,997 - root - INFO - 导入主模块...
2025-05-23 17:15:16,429 - root - INFO - 启动主模块...
2025-05-23 17:15:16,434 - root - INFO - 准备启动GUI...
2025-05-23 17:15:16,435 - root - INFO - 测试GUI环境...
2025-05-23 17:15:16,660 - root - INFO - GUI环境测试通过
2025-05-23 17:15:16,677 - root - INFO - 导入GUI模块...
2025-05-23 17:15:16,693 - root - INFO - 正在创建主窗口...
2025-05-23 17:15:16,807 - root - INFO - GUI启动成功
2025-05-23 17:20:22,351 - root - INFO - ==================================================
2025-05-23 17:20:22,352 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:20:22,352 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:20:22,352 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:20:22,352 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:20:22,352 - root - INFO - 导入主模块...
2025-05-23 17:20:22,810 - root - INFO - 启动主模块...
2025-05-23 17:20:22,814 - root - INFO - 准备启动GUI...
2025-05-23 17:20:22,814 - root - INFO - 测试GUI环境...
2025-05-23 17:20:23,040 - root - INFO - GUI环境测试通过
2025-05-23 17:20:23,059 - root - INFO - 导入GUI模块...
2025-05-23 17:20:23,082 - root - INFO - 正在创建主窗口...
2025-05-23 17:20:23,190 - root - INFO - GUI启动成功
2025-05-23 17:26:48,671 - root - INFO - ==================================================
2025-05-23 17:26:48,672 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:26:48,672 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:26:48,672 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:26:48,672 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:26:48,672 - root - INFO - 导入主模块...
2025-05-23 17:26:54,113 - root - INFO - 启动主模块...
2025-05-23 17:26:54,118 - root - INFO - 准备启动GUI...
2025-05-23 17:26:54,118 - root - INFO - 测试GUI环境...
2025-05-23 17:26:54,378 - root - INFO - GUI环境测试通过
2025-05-23 17:26:54,397 - root - INFO - 导入GUI模块...
2025-05-23 17:26:54,401 - root - ERROR - GUI启动失败: unindent does not match any outer indentation level (gui.py, line 352)
2025-05-23 17:26:54,401 - root - ERROR - 程序出错: unindent does not match any outer indentation level (gui.py, line 352)
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\main.py", line 206, in main
    launch_gui()
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\main.py", line 46, in launch_gui
    from h3c_doc_checker.gui import DocumentCheckerGUI
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\gui.py", line 352
    def run_gui():
                  ^
IndentationError: unindent does not match any outer indentation level
2025-05-23 17:27:34,003 - root - INFO - ==================================================
2025-05-23 17:27:34,003 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:27:34,003 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:27:34,003 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:27:34,003 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:27:34,003 - root - INFO - 导入主模块...
2025-05-23 17:27:34,389 - root - INFO - 启动主模块...
2025-05-23 17:27:34,393 - root - INFO - 准备启动GUI...
2025-05-23 17:27:34,393 - root - INFO - 测试GUI环境...
2025-05-23 17:27:34,608 - root - INFO - GUI环境测试通过
2025-05-23 17:27:34,627 - root - INFO - 导入GUI模块...
2025-05-23 17:27:34,649 - root - INFO - 正在创建主窗口...
2025-05-23 17:27:34,771 - root - ERROR - GUI启动失败: 'DocumentCheckerGUI' object has no attribute 'reset_tool'
2025-05-23 17:27:34,771 - root - ERROR - 程序出错: 'DocumentCheckerGUI' object has no attribute 'reset_tool'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\main.py", line 206, in main
    launch_gui()
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\main.py", line 51, in launch_gui
    app = DocumentCheckerGUI(root)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\gui.py", line 20, in __init__
    self.create_widgets()
  File "C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_doc_checker\gui.py", line 31, in create_widgets
    ttk.Button(btn_frame, text="重置", command=self.reset_tool).pack(side=tk.LEFT, padx=5)
                                               ^^^^^^^^^^^^^^^
AttributeError: 'DocumentCheckerGUI' object has no attribute 'reset_tool'
2025-05-23 17:35:10,610 - root - INFO - ==================================================
2025-05-23 17:35:10,610 - root - INFO - H3C Doc Checker 启动
2025-05-23 17:35:10,610 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 17:35:10,611 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 17:35:10,611 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 17:35:10,611 - root - INFO - 导入主模块...
2025-05-23 17:35:11,104 - root - INFO - 启动主模块...
2025-05-23 17:35:11,108 - root - INFO - 准备启动GUI...
2025-05-23 17:35:11,108 - root - INFO - 测试GUI环境...
2025-05-23 17:35:11,371 - root - INFO - GUI环境测试通过
2025-05-23 17:35:11,399 - root - INFO - 导入GUI模块...
2025-05-23 17:35:11,419 - root - INFO - 正在创建主窗口...
2025-05-23 17:35:11,534 - root - INFO - GUI启动成功
2025-05-23 18:06:16,247 - root - INFO - ==================================================
2025-05-23 18:06:16,247 - root - INFO - H3C Doc Checker 启动
2025-05-23 18:06:16,247 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 18:06:16,248 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 18:06:16,248 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 18:06:16,248 - root - INFO - 导入主模块...
2025-05-23 18:06:16,596 - root - INFO - 启动主模块...
2025-05-23 18:06:16,602 - root - INFO - 准备启动GUI...
2025-05-23 18:06:16,602 - root - INFO - 测试GUI环境...
2025-05-23 18:06:16,896 - root - INFO - GUI环境测试通过
2025-05-23 18:06:16,916 - root - INFO - 导入GUI模块...
2025-05-23 18:06:16,938 - root - INFO - 正在创建主窗口...
2025-05-23 18:06:17,058 - root - INFO - GUI启动成功
2025-05-23 18:07:35,327 - root - INFO - ==================================================
2025-05-23 18:07:35,327 - root - INFO - H3C Doc Checker 启动
2025-05-23 18:07:35,327 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 18:07:35,328 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 18:07:35,328 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 18:07:35,328 - root - INFO - 导入主模块...
2025-05-23 18:07:35,668 - root - INFO - 启动主模块...
2025-05-23 18:07:35,681 - root - INFO - 准备启动GUI...
2025-05-23 18:07:35,681 - root - INFO - 测试GUI环境...
2025-05-23 18:07:35,904 - root - INFO - GUI环境测试通过
2025-05-23 18:07:35,928 - root - INFO - 导入GUI模块...
2025-05-23 18:07:35,948 - root - INFO - 正在创建主窗口...
2025-05-23 18:07:36,084 - root - INFO - GUI启动成功
2025-05-23 18:09:07,067 - root - INFO - ==================================================
2025-05-23 18:09:07,067 - root - INFO - H3C Doc Checker 启动
2025-05-23 18:09:07,067 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-23 18:09:07,067 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-23 18:09:07,068 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-23 18:09:07,068 - root - INFO - 导入主模块...
2025-05-23 18:09:07,429 - root - INFO - 启动主模块...
2025-05-23 18:09:07,435 - root - INFO - 准备启动GUI...
2025-05-23 18:09:07,435 - root - INFO - 测试GUI环境...
2025-05-23 18:09:07,715 - root - INFO - GUI环境测试通过
2025-05-23 18:09:07,736 - root - INFO - 导入GUI模块...
2025-05-23 18:09:07,758 - root - INFO - 正在创建主窗口...
2025-05-23 18:09:07,888 - root - INFO - GUI启动成功
2025-05-26 09:49:34,790 - root - INFO - ==================================================
2025-05-26 09:49:34,790 - root - INFO - H3C Doc Checker 启动
2025-05-26 09:49:34,791 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-26 09:49:34,791 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-26 09:49:34,791 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-26 09:49:34,791 - root - INFO - 导入主模块...
2025-05-26 09:49:35,246 - root - INFO - 启动主模块...
2025-05-26 09:49:35,251 - root - INFO - 准备启动GUI...
2025-05-26 09:49:35,251 - root - INFO - 测试GUI环境...
2025-05-26 09:49:35,537 - root - INFO - GUI环境测试通过
2025-05-26 09:49:35,560 - root - INFO - 导入GUI模块...
2025-05-26 09:49:35,619 - root - INFO - 正在创建主窗口...
2025-05-26 09:49:35,747 - root - INFO - GUI启动成功
2025-05-26 09:50:00,114 - root - INFO - ==================================================
2025-05-26 09:50:00,114 - root - INFO - H3C Doc Checker 启动
2025-05-26 09:50:00,115 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-26 09:50:00,115 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-26 09:50:00,115 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-26 09:50:00,115 - root - INFO - 导入主模块...
2025-05-26 09:50:00,660 - root - INFO - 启动主模块...
2025-05-26 09:50:00,664 - root - INFO - 准备启动GUI...
2025-05-26 09:50:00,665 - root - INFO - 测试GUI环境...
2025-05-26 09:50:00,945 - root - INFO - GUI环境测试通过
2025-05-26 09:50:00,969 - root - INFO - 导入GUI模块...
2025-05-26 09:50:00,989 - root - INFO - 正在创建主窗口...
2025-05-26 09:50:01,125 - root - INFO - GUI启动成功
2025-05-26 09:54:37,293 - root - INFO - ==================================================
2025-05-26 09:54:37,293 - root - INFO - H3C Doc Checker 启动
2025-05-26 09:54:37,294 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-26 09:54:37,294 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-26 09:54:37,294 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-26 09:54:37,294 - root - INFO - 导入主模块...
2025-05-26 09:54:37,792 - root - INFO - 启动主模块...
2025-05-26 09:54:37,796 - root - INFO - 准备启动GUI...
2025-05-26 09:54:37,796 - root - INFO - 测试GUI环境...
2025-05-26 09:54:38,045 - root - INFO - GUI环境测试通过
2025-05-26 09:54:38,061 - root - INFO - 导入GUI模块...
2025-05-26 09:54:38,082 - root - INFO - 正在创建主窗口...
2025-05-26 09:54:38,219 - root - INFO - GUI启动成功
2025-05-26 09:56:40,124 - root - INFO - ==================================================
2025-05-26 09:56:40,124 - root - INFO - H3C Doc Checker 启动
2025-05-26 09:56:40,124 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-26 09:56:40,125 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-26 09:56:40,125 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-26 09:56:40,125 - root - INFO - 导入主模块...
2025-05-26 09:56:40,607 - root - INFO - 启动主模块...
2025-05-26 09:56:40,611 - root - INFO - 准备启动GUI...
2025-05-26 09:56:40,611 - root - INFO - 测试GUI环境...
2025-05-26 09:56:40,861 - root - INFO - GUI环境测试通过
2025-05-26 09:56:40,880 - root - INFO - 导入GUI模块...
2025-05-26 09:56:40,901 - root - INFO - 正在创建主窗口...
2025-05-26 09:56:41,018 - root - INFO - GUI启动成功
2025-05-26 20:10:32,212 - root - INFO - ==================================================
2025-05-26 20:10:32,212 - root - INFO - H3C Doc Checker 启动
2025-05-26 20:10:32,213 - root - INFO - Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-05-26 20:10:32,213 - root - INFO - 工作目录: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check
2025-05-26 20:10:32,213 - root - INFO - 日志文件: C:\Users\<USER>\Documents\GitHub\wwshell-work\H3C\check\h3c_checker.log
2025-05-26 20:10:32,213 - root - INFO - 导入主模块...
2025-05-26 20:10:32,544 - root - INFO - 启动主模块...
2025-05-26 20:10:32,549 - root - INFO - 准备启动GUI...
2025-05-26 20:10:32,549 - root - INFO - 测试GUI环境...
2025-05-26 20:10:32,791 - root - INFO - GUI环境测试通过
2025-05-26 20:10:32,810 - root - INFO - 导入GUI模块...
2025-05-26 20:10:32,835 - root - INFO - 正在创建主窗口...
2025-05-26 20:10:32,969 - root - INFO - GUI启动成功
